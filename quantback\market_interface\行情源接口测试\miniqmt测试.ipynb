from xtquant import xtdata
import pandas as pd

# 下载k线数据(包含复权因子)
xtdata.download_history_data(
    '000029.SZ', '1m', start_time='20250813', end_time='', incrementally=False
)

# 测试获取行业列表
xtdata.download_sector_data()
sector_list = xtdata.get_sector_list()
sector_list

# 测试获取行业股票列表
stocks = xtdata.get_stock_list_in_sector('沪深A股')
stocks[:10]

# 测试获取K线数据-日线
stock_code = '000029.SZ'
result: dict = xtdata.get_market_data_ex(
    [],
    [stock_code],
    period='1d',
    start_time='20160401',
    end_time='',
    count=5,
    dividend_type='back_ratio',
    fill_data=False,
)
df = result[stock_code]
# print("index的数据类型:", df.index.dtype)
# print("列的数据类型:\n", df.dtypes)
df

# 测试获取K线数据-1分钟
stock_code = '000029.SZ'
result: dict = xtdata.get_market_data_ex(
    [],
    [stock_code],
    period='1m',
    start_time='20250101',
    end_time='',
    count=400,
    dividend_type='front_ratio',
    fill_data=False,
)
df = result[stock_code]
df

# 本接口暂时未受国金qmt客户端支持
stock_code = '000029.SZ'
result = xtdata.get_full_kline(
    field_list=[],
    stock_list=[stock_code],
    period='1m',
    start_time='',
    end_time='',
    count=300,
    dividend_type='none',
    fill_data=True,
)

df = result[stock_code]
df

stock_code = '159919.SZ'  # '600519.SH'
df = xtdata.get_divid_factors(stock_code, start_time='19900101', end_time='')
df['time'] = pd.to_datetime(df['time'], unit='ms')
# df.set_index("time", inplace=True)
df

stock_code = '000029.SZ'
result = xtdata.get_full_tick([stock_code])
data = result[stock_code]
data

data = xtdata.get_instrument_detail(stock_code, False)
data