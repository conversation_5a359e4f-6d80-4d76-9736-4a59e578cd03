"""
投资组合管理和交易执行
"""

import logging
from dataclasses import dataclass
from datetime import date, datetime, time
from functools import lru_cache
from typing import Dict, List, Optional

from quantback.market_interface.market_data import get_kline_data

from .orders import Order, OrderManager, OrderSide, OrderStatus, OrderStyle, OrderType

logger = logging.getLogger(__name__)


@dataclass
class OrderCost:
    """
    交易成本配置类

    Attributes:
        open_tax: 买入时印花税 (只股票类标的收取，基金与期货不收)
        close_tax: 卖出时印花税 (只股票类标的收取，基金与期货不收)
        open_commission: 买入时佣金
        close_commission: 卖出时佣金
        close_today_commission: 平今仓佣金
        min_commission: 最低佣金，不包含印花税
    """

    open_tax: float = 0.0
    close_tax: float = 0.001  # 默认千分之一印花税
    open_commission: float = 0.0003  # 默认万分之三佣金
    close_commission: float = 0.0003  # 默认万分之三佣金
    close_today_commission: float = 0.0  # 默认平今仓佣金为0
    min_commission: float = 5.0  # 默认最低佣金5元

    def calculate_max_buy(self, price: float, available_cash: float) -> int:
        """
        计算最大可买入数量

        Args:
            price: 股票价格
            available_cash: 可用资金

        Returns:
            int: 最大可买入数量
        """
        # 保险起见的算法, 实际未必花费min_commission
        max_value = (available_cash - self.min_commission) / (
            1 + self.open_tax + self.open_commission
        )
        return int(max_value / price)

    def calculate_cost(
        self, price: float, volume: int, is_buy: bool, is_today: bool = False
    ) -> float:
        """
        计算交易成本

        Args:
            price: 成交价格
            volume: 交易数量（股数）
            is_buy: 是否为买入
            is_today: 是否为平今仓（仅对卖出有效）

        Returns:
            float: 交易成本
        """
        # 计算交易金额
        trade_value = price * volume

        # 计算印花税
        if is_buy:
            tax = trade_value * self.open_tax
        else:
            tax = trade_value * self.close_tax

        # 计算佣金
        if is_buy:
            commission = trade_value * self.open_commission
        else:
            if is_today:
                commission = trade_value * self.close_today_commission
            else:
                commission = trade_value * self.close_commission

        # 应用最低佣金限制（不包含印花税）
        commission = max(commission, self.min_commission)

        # 总成本 = 印花税 + 佣金
        cost = tax + commission

        return cost

    def __str__(self) -> str:
        """返回成本配置的字符串表示"""
        return (
            f'OrderCost(open_tax={self.open_tax}, close_tax={self.close_tax}, '
            f'open_commission={self.open_commission}, close_commission={self.close_commission}, '
            f'close_today_commission={self.close_today_commission}, min_commission={self.min_commission})'
        )


# 预定义的常用交易成本配置
DEFAULT_STOCK_COST = OrderCost()  # 使用默认值，适用于股票交易

DEFAULT_FUND_COST = OrderCost(
    open_tax=0.0,
    close_tax=0.0,  # 基金无印花税
    open_commission=0.0003,  # 万分之三佣金
    close_commission=0.0003,  # 万分之三佣金
    close_today_commission=0.0,
    min_commission=5.0,  # 最低5元佣金
)


class Position:
    """
    持仓类

    ⚠️ 重要约定：
    Position 对象仅用于读取持仓信息，请勿直接修改其任何属性！
    所有持仓变更必须通过正规交易函数进行：
    - order() - 下单
    - order_value() - 按金额下单
    - order_target() - 调整到目标持仓
    - order_target_value() - 调整到目标金额

    正确用法 ✅:
        if '000001.SZ' in context.portfolio.positions:
            position = context.portfolio.positions['000001.SZ']
            current_volume = position.total_volume  # 读取持仓
        order_target(symbol, target_volume)         # 通过下单修改持仓

    错误用法 ❌:
        position.total_volume = 1000  # 禁止直接修改
        position.avg_cost = 10.0      # 禁止直接修改
    """

    def __init__(self, symbol: str):
        self.symbol = symbol
        self.total_volume = 0  # 总持仓数量
        self.closeable_volume = 0  # 可卖数量
        self.avg_cost = 0.0  # 平均成本
        self.price = 0.0  # 当前价格
        self.value = 0.0  # 持仓价值

    def update_price(self, price: float):
        """更新当前价格"""
        self.price = price
        self.value = self.total_volume * price


class Portfolio:
    """投资组合管理器"""

    def __init__(self, initial_cash: float = 1000000.0, context=None):
        """
        初始化投资组合

        Args:
            initial_cash: 初始资金
            context: 上下文对象引用
        """
        self.starting_cash = initial_cash
        self.available_cash = initial_cash
        self.locked_cash = 0.0

        # 上下文引用，用于获取当前时间等信息
        self.context = context

        # 持仓管理
        self.positions: Dict[str, Position] = {}

        # 订单管理
        self.order_manager = OrderManager()

        # 交易成本配置
        self.order_costs: Dict[str, OrderCost] = {}

        # 历史记录
        self.history = {
            'total_value': [],
            'available_cash': [],
            'market_value': [],
            'timestamp': [],
            'positions': [],  # 每日持仓快照
        }

    def lock_cash(self, cash_amount: float) -> bool:
        """锁定资金"""
        if cash_amount > self.available_cash:
            return False
        self.available_cash -= cash_amount
        self.locked_cash += cash_amount
        return True

    def unlock_cash(self, cash_amount: float):
        """释放锁定资金"""
        if cash_amount > self.locked_cash:
            cash_amount = self.locked_cash  # 防止负数
        self.locked_cash -= cash_amount
        self.available_cash += cash_amount

    def lock_position(self, symbol: str, volume: int) -> bool:
        """锁定持仓股票（用于限价卖单）"""
        if symbol not in self.positions:
            return False

        position = self.positions[symbol]
        if volume > position.closeable_volume:
            return False

        position.closeable_volume -= volume
        return True

    def unlock_position(self, symbol: str, volume: int):
        """释放锁定的持仓股票"""
        if symbol in self.positions:
            self.positions[symbol].closeable_volume += volume

    def unlock_all(self):
        """每日收盘时释放冻结的持仓和资金, 同时使得t日买入的，t+1日可以卖出"""
        for position in self.positions.values():
            position.closeable_volume = position.total_volume
        self.available_cash += self.locked_cash
        self.locked_cash = 0.0

    @property
    def market_value(self) -> float:
        """当前持仓市值（即时计算）"""
        total_market_value = 0.0
        for position in self.positions.values():
            total_market_value += position.value
        return total_market_value

    @property
    def total_value(self) -> float:
        """总资产（即时计算）"""
        return self.available_cash + self.locked_cash + self.market_value

    def set_order_cost(self, cost: OrderCost, type: str):
        """
        设置交易成本

        Args:
            cost: OrderCost 对象
            type: 类型，如 'stock'
        """
        self.order_costs[type] = cost

    def order(
        self,
        symbol: str,
        volume: int,
        style: OrderStyle,
    ) -> Optional[str]:
        """
        下单交易

        Args:
            symbol: 股票代码
            volume: 委托数量，正数买入，负数卖出
            style: 订单样式

        Returns:
            Optional[str]: 订单ID，失败返回None
        """
        # 检查是否在交易时间内
        if not self._is_in_trading_hours():
            return None

        # 从样式中获取订单类型和价格
        order_type = style.get_order_type()
        price = style.get_price()

        # 限价单必须有价格
        if order_type == OrderType.LIMIT and price is None:
            logger.error('限价单必须指定价格')
            return None

        if volume == 0:
            return None

        market_data = self.get_current_market_data(symbol, self.context.current_dt)
        if market_data is None:
            logger.error(f'获取 {symbol} 市场数据失败，无法下单')
            return None

        # 智能调整委托价格，确保不超出涨跌停限制
        adjusted_price = self._adjust_order_price(symbol, price, order_type, market_data)

        side = OrderSide.BUY if volume > 0 else OrderSide.SELL
        order_volume = abs(volume)

        # 智能调整委托数量
        adjusted_volume = self._adjust_order_volume(
            symbol, side, order_volume, order_type, adjusted_price, market_data
        )
        if adjusted_volume <= 0:
            return None

        # 如果价格被调整，输出提示信息
        if adjusted_price != price:
            action = '买入' if side == OrderSide.BUY else '卖出'
            logger.debug(f'委托价格调整: {symbol} {action} {price:.2f} → {adjusted_price:.2f}')

        # 如果数量被调整，输出提示信息
        if adjusted_volume != order_volume:
            action = '买入' if side == OrderSide.BUY else '卖出'
            logger.debug(f'委托数量调整: {symbol} {action} {order_volume} → {adjusted_volume}')

        # 创建订单
        order_id = self.order_manager.create_order(
            symbol=symbol,
            side=side,
            volume=adjusted_volume,
            order_type=order_type,
            price=adjusted_price,
        )
        logger.info(
            f'创建委托: {order_id[:8]} {order_type.name} {side.name} {symbol} {adjusted_volume}股 {adjusted_price}元'
        )

        # 立即尝试执行订单
        self._try_immediate_execution(order_id, self.context.current_dt, market_data)

        return order_id

    def _is_in_trading_hours(self) -> bool:
        """
        判断是否在交易时间内

        通过检查当前时间是否在交易时间内来判断
        """

        current_time = self.context.current_dt.time()

        # 检查是否在交易时间内（9:30-11:30, 13:01-15:00）

        return (time(9, 30) <= current_time <= time(11, 30)) or (
            time(13, 1) <= current_time <= time(15, 00)
        )

    def order_value(
        self,
        symbol: str,
        value: float,
        style: OrderStyle,
    ) -> Optional[str]:
        """
        按委托金额下单

        Args:
            symbol: 股票代码
            value: 委托金额，正数买入，负数卖出
            style: 订单样式

        Returns:
            Optional[str]: 订单ID
        """
        if value == 0:
            return None

        # 从样式中获取价格
        order_type = style.get_order_type()
        price = style.get_price()

        # 对于市价单，需要获取当前价格来计算委托数量
        if order_type == OrderType.MARKET and price is None:
            # 获取当前市场数据
            market_data = self.get_current_market_data(symbol, self.context.current_dt)
            if market_data is None:
                logger.error(f'获取 {symbol} 市场数据失败，无法下单')
                return None

            price = market_data['close']  # 使用当前价格

        # 计算委托数量
        volume = int(value / price)  # 正数买入，负数卖出
        return self.order(symbol, volume, style)

    def order_target(
        self,
        symbol: str,
        target_volume: int,
        style: OrderStyle,
    ) -> Optional[str]:
        """
        调整持仓到目标数量

        Args:
            symbol: 股票代码
            target_volume: 目标持仓数量
            style: 订单样式

        Returns:
            Optional[str]: 订单ID
        """
        current_volume = 0
        if symbol in self.positions:
            current_volume = self.positions[symbol].total_volume

        diff_volume = target_volume - current_volume
        if diff_volume == 0:  # 整数比较，无需浮点数精度处理
            return None

        # order函数已经包含立即执行逻辑，直接调用即可
        return self.order(symbol, diff_volume, style)

    def order_target_value(
        self,
        symbol: str,
        target_value: float,
        style: OrderStyle,
    ) -> Optional[str]:
        """
        调整持仓到目标金额

        Args:
            symbol: 股票代码
            target_value: 目标持仓金额
            style: 订单样式

        Returns:
            Optional[str]: 订单ID
        """
        # 从样式中获取价格
        order_type = style.get_order_type()
        price = style.get_price()

        # 对于市价单，需要获取当前价格来计算目标数量
        if order_type == OrderType.MARKET and price is None:
            # 获取当前市场数据
            market_data = self.get_current_market_data(symbol, self.context.current_dt)
            if market_data is None:
                logger.error(f'获取 {symbol} 市场数据失败，无法下单')
                return None

            price = market_data['close']  # 使用当前价格

        target_volume = int(target_value / price)
        return self.order_target(symbol, target_volume, style)

    def cancel_portfolio_order(self, order_id: str) -> bool:
        """
        取消订单

        Args:
            order_id: 订单ID

        Returns:
            bool: 是否成功
        """
        # 解冻
        order = self.order_manager.orders.get(order_id)
        if order:
            self._release_frozen_resources(order)
            return self.order_manager.cancel_order(order.order_id)
        else:
            return False

    def _execute_single_order(self, order: Order, execution_price: float) -> None:
        """
        执行单个订单

        Args:
            order: 订单对象
            execution_price: 执行价格

        Raises:
            RuntimeError: 当出现资金不足或持仓不足时，表明系统内部逻辑错误
        """
        # 释放冻结的资源
        self._release_frozen_resources(order)

        symbol = order.symbol
        side = order.side
        volume = order.volume

        order_cost = self.order_costs.get('stock')
        is_buy = side == OrderSide.BUY
        commission = order_cost.calculate_cost(execution_price, volume, is_buy)

        open_price = 0.0  # 记录开仓价格, 用于计算平仓盈亏

        if side == OrderSide.BUY:
            total_cost = execution_price * volume + commission
            if total_cost > self.available_cash:
                raise RuntimeError(
                    f'系统内部错误：执行买单时资金不足，需要 {total_cost:.2f}，可用 {self.available_cash:.2f}。'
                    f'这表明资金检查或冻结逻辑存在bug。'
                )
            self.available_cash -= total_cost
            # 更新持仓
            if symbol not in self.positions:
                self.positions[symbol] = Position(symbol)

            # 更新平均成本
            if self.positions[symbol].total_volume == 0:
                self.positions[symbol].avg_cost = execution_price
            else:
                total_cost = (
                    self.positions[symbol].avg_cost * self.positions[symbol].total_volume
                    + execution_price * volume
                )
                self.positions[symbol].avg_cost = total_cost / (
                    self.positions[symbol].total_volume + volume
                )

            self.positions[symbol].total_volume += volume
            # 不改变closeable_volume, 当日买入的股票需要第二天才能卖出

        else:
            if symbol not in self.positions:
                raise RuntimeError(
                    f'系统内部错误：执行卖单时无持仓 {symbol}。这表明持仓检查逻辑存在bug。'
                )
            position = self.positions[symbol]
            if volume > position.closeable_volume:
                raise RuntimeError(
                    f'系统内部错误：执行卖单时持仓不足，{symbol} 需要 {volume}，可用 {position.closeable_volume}。'
                    f'这表明持仓检查或冻结逻辑存在bug。'
                )
            position.total_volume -= volume
            position.closeable_volume -= volume
            total_proceeds = execution_price * volume - commission
            self.available_cash += total_proceeds
            if position.total_volume <= 0:
                del self.positions[symbol]
            open_price = position.avg_cost
        # 使用当前交易日作为成交时间
        fill_time = self.context.current_dt

        self.order_manager.fill_order(
            order.order_id, execution_price, volume, commission, fill_time, open_price
        )

    def _adjust_order_price(
        self,
        symbol: str,
        price: Optional[float],
        order_type: OrderType,
        market_data: Dict[str, float],
    ) -> Optional[float]:
        """
        智能调整委托价格，确保不超出涨跌停限制

        Args:
            symbol: 股票代码
            price: 原始委托价格
            order_type: 订单类型

        Returns:
            Optional[float]: 调整后的价格，None表示无法下单
        """
        # 市价单不需要调整价格
        if order_type == OrderType.MARKET:
            return price

        high_limit = market_data['high_limit']
        low_limit = market_data['low_limit']

        price = max(min(price, high_limit), low_limit)

        return price

    def _adjust_order_volume(
        self,
        symbol: str,
        side: OrderSide,
        volume: int,
        order_type: OrderType,
        price: Optional[float],
        market_data: Dict[str, float],
    ) -> int:
        """
        智能调整委托数量

        Args:
            symbol: 股票代码
            side: 买卖方向
            volume: 原始委托数量
            order_type: 订单类型
            price: 委托价格

        Returns:
            int: 调整后的有效数量，0表示无法下单
        """
        if volume <= 0:
            return 0

        if side == OrderSide.BUY:
            return self._adjust_buy_volume(symbol, volume, order_type, price, market_data)
        else:
            return self._adjust_sell_volume(symbol, volume)

    def _adjust_buy_volume(
        self,
        symbol: str,
        volume: int,
        order_type: OrderType,
        price: Optional[float],
        market_data: Dict[str, float],
    ) -> int:
        """
        调整买单数量

        Args:
            symbol: 股票代码
            volume: 原始委托数量
            order_type: 订单类型
            price: 委托价格

        Returns:
            int: 调整后的买入数量
        """
        # 获取估算价格
        estimated_price = price
        if order_type == OrderType.MARKET and estimated_price is None:
            estimated_price = market_data['close']

        # 获取交易成本配置
        order_cost = self.order_costs.get('stock')

        # 计算最大可买数量
        max_buyable = order_cost.calculate_max_buy(estimated_price, self.available_cash)

        if max_buyable <= 0:
            logger.warning(f'资金不足，无法买入 {symbol}')
            return 0

        adjusted_volume = min(volume, max_buyable)

        # 调整到最大可买数量，并向下取整到100的整数倍
        adjusted_volume = (adjusted_volume // 100) * 100

        if adjusted_volume <= 0:
            logger.warning('调整后数量不足100股，买单失败')
            return 0

        return adjusted_volume

    def _adjust_sell_volume(self, symbol: str, volume: int) -> int:
        """
        调整卖单数量

        Args:
            symbol: 股票代码
            volume: 原始委托数量

        Returns:
            int: 调整后的卖出数量
        """
        # 检查是否有持仓
        if symbol not in self.positions:
            logger.warning(f'无持仓 {symbol}，卖单失败')
            return 0

        position = self.positions[symbol]
        max_sellable = position.closeable_volume

        if max_sellable <= 0:
            logger.warning(f'{symbol} 无可卖持仓，卖单失败')
            return 0

        adjusted_volume = min(volume, max_sellable)

        # 特殊情况：如果是完全平仓（卖出全部持仓），不需要取整到100的整数倍
        if adjusted_volume == position.total_volume:
            return adjusted_volume

        # 通常情况下向下取整到100的整数倍
        adjusted_volume = (adjusted_volume // 100) * 100

        if adjusted_volume <= 0:
            logger.warning('调整后数量不足100股，卖单失败')
            return 0

        return adjusted_volume

    def get_trade_records(self) -> List[Dict]:
        """获取交易记录"""
        return self.order_manager.get_trade_records()

    def _try_immediate_execution(
        self, order_id: str, current_time: datetime, market_data: Dict[str, float]
    ):
        """
        尝试立即执行订单（在定时任务函数中调用order时）

        Args:
            order_id: 订单ID
            current_time: 当前执行时间
        """
        order = self.order_manager.orders.get(order_id)
        if not order or order.status != OrderStatus.PENDING:
            return

        if order.order_type == OrderType.MARKET:
            # 市价单立即成交
            self._execute_market_order_immediately(order, market_data, current_time)
        elif order.order_type == OrderType.LIMIT:
            # 限价单检查条件
            self._execute_limit_order_immediately_or_wait(order, market_data, current_time)

    def get_current_market_data(self, symbol: str, current_time: datetime) -> Optional[Dict]:
        """
        获取当前时间的市场数据

        Args:
            symbol: 股票代码
            current_time: 当前时间

        Returns:
            Optional[Dict]: 市场数据
        """
        try:
            # 格式化时间为查询需要的格式
            date_str = current_time.strftime('%Y-%m-%d')
            intraday_minute_data = Portfolio._get_intraday_minute_data(symbol, date_str)
            if intraday_minute_data is None:
                return None
            df = intraday_minute_data.get('minute_kline')
            if df is not None and len(df) > 0:
                # 查找当前时间的数据
                target_time = current_time.replace(second=0, microsecond=0)

                # 特殊处理9:30时刻：使用9:31的open价格, 也是当日的open价格.
                # 构建基础返回数据
                base_data = {
                    'day_open': intraday_minute_data['day_open'],
                    'day_pre_close': intraday_minute_data['day_pre_close'],
                    'high_limit': intraday_minute_data['high_limit'],
                    'low_limit': intraday_minute_data['low_limit'],
                }

                if target_time.hour == 9 and target_time.minute == 30:
                    # 9:30时刻使用开盘价
                    return {
                        **base_data,
                        'open': intraday_minute_data['day_open'],
                        'high': intraday_minute_data['day_open'],
                        'low': intraday_minute_data['day_open'],
                        'close': intraday_minute_data['day_open'],
                    }

                # 其他时间正常查找
                matching_rows = df[df.index == target_time]
                if len(matching_rows) > 0:
                    row = matching_rows.iloc[0]
                    return {
                        **base_data,
                        'open': row['open'],
                        'high': row['high'],
                        'low': row['low'],
                        'close': row['close'],
                    }

        except Exception as e:
            logger.error(f'获取市场数据失败: {e}')
        return None

    def _execute_market_order_immediately(self, order, market_data: Dict, current_time: datetime):
        """
        立即执行市价单

        Args:
            order: 订单对象
            market_data: 市场数据
            current_time: 当前时间
        """
        # 检查涨跌停限制
        close_price = market_data['close']
        high_limit = market_data['high_limit']
        low_limit = market_data['low_limit']

        can_execute = True
        # 买单：如果收盘价等于涨停价，买不上
        if order.side == OrderSide.BUY:
            if high_limit is not None and abs(close_price - high_limit) < 0.01:
                can_execute = False

        # 卖单：如果收盘价等于跌停价，卖不出
        elif order.side == OrderSide.SELL:
            if low_limit is not None and abs(close_price - low_limit) < 0.01:
                can_execute = False

        # 确定成交价格
        execution_price = close_price

        if can_execute:
            # 立即成交
            self._execute_single_order(order, execution_price)
            logger.info(
                f'立即成交: {order.order_id[:8]} {order.order_type.name} {order.side.name} {order.symbol} {order.filled_volume}股 {execution_price:.2f}元'
            )
        else:
            # 无法成交，取消订单
            self.order_manager.cancel_order(order.order_id)
            logger.warning(f'✗ 市价单 {order.order_id[:8]} 因涨跌停限制无法成交，已取消')

    def _execute_limit_order_immediately_or_wait(
        self, order, market_data: Dict, current_time: datetime
    ):
        """
        立即检查并执行限价单，或等待成交

        Args:
            order: 订单对象
            market_data: 市场数据
            current_time: 当前时间
        """
        close_price = market_data['close']

        # 检查成交条件, 要求委托价格严格大于当前价, 否则未必撮合成功.
        can_execute = False
        if order.side == OrderSide.BUY and close_price < order.price:
            can_execute = True
        elif order.side == OrderSide.SELL and close_price > order.price:
            can_execute = True

        # 确定成交价格
        execution_price = close_price

        if can_execute:
            # 立即成交
            self._execute_single_order(order, execution_price)
            logger.info(
                f'立即成交: {order.order_id[:8]} {order.order_type.name} {order.side.name} {order.symbol} {order.filled_volume}股 {execution_price:.2f}元'
            )
        else:
            # 不满足条件，冻结资金或股票
            self._freeze_order_resources(order)

            # 预计算成交时间（性能优化）
            self._precalculate_fill_time(order, current_time)

            logger.info(
                f'等待成交: {order.order_id[:8]} {order.order_type.name} {order.side.name} {order.symbol} {order.filled_volume}股 已冻结资源'
            )

    def _freeze_order_resources(self, order):
        """
        冻结订单所需的资源（资金或股票）

        Args:
            order: 订单对象
        """
        if order.side == OrderSide.BUY:
            # 买单：冻结资金
            order_cost = self.order_costs.get('stock')
            if order_cost:
                commission = order_cost.calculate_cost(
                    order.price, order.volume, True
                )  # 此处为限价单冻结资金, price不会是None
                required_cash = order.price * order.volume + commission
            else:
                required_cash = order.price * order.volume * 1.0003  # 默认0.03%佣金

            if self.lock_cash(required_cash):
                order.frozen_cash = required_cash
                logger.debug(f'  冻结资金: {required_cash:.2f}')
            else:
                raise RuntimeError('系统内部错误：冻结资金失败，表明资金检查逻辑存在bug。')

        elif order.side == OrderSide.SELL:
            # 卖单：冻结股票
            if self.lock_position(order.symbol, order.volume):
                order.frozen_volume = order.volume
                logger.debug(f'  冻结股票: {order.volume}')
            else:
                raise RuntimeError('系统内部错误：冻结股票失败，表明持仓检查逻辑存在bug。')

    def _precalculate_fill_time(self, order, current_time: datetime):
        """
        预计算限价单的成交时间（性能优化）

        Args:
            order: 限价单对象
            current_time: 当前时间
        """
        try:
            intraday_minute_data = Portfolio._get_intraday_minute_data(
                order.symbol, current_time.strftime('%Y-%m-%d')
            )
            if intraday_minute_data is None:
                logger.debug(f'  无法获取 {order.symbol} 的市场数据，跳过预计算')
                return
            minute_kline = intraday_minute_data.get('minute_kline')
            if minute_kline is None:
                logger.debug(f'  无法获取 {order.symbol} 的分钟K线数据，跳过预计算')
                return

            # 根据订单类型和价格计算成交时间
            fill_time = None
            if minute_kline is None or minute_kline.empty:
                logger.debug(f'    K线数据为空，无法预计算')
            else:
                logger.debug(
                    f'    分析K线数据：{len(minute_kline)}条记录，时间范围 {minute_kline.index[0]} 到 {minute_kline.index[-1]}'
                )
                logger.debug(f'    当前时间：{current_time} (类型: {type(current_time)})')
                logger.debug(f'    订单：{order.side.name} {order.symbol} @ {order.price:.2f}')

                # 只考虑当前时间之后的数据
                try:
                    future_data = minute_kline[minute_kline.index > current_time]
                    if future_data.empty:
                        logger.debug(f'    当前时间之后无数据，无法预计算')
                    else:
                        logger.debug(f'    未来数据：{len(future_data)}条记录')

                        # 根据买卖方向判断成交条件
                        if order.side == OrderSide.BUY:
                            # 买单：需要low <= 限价
                            matching_rows = future_data[future_data['low'] < order.price]
                            logger.debug(f'    买单条件：寻找 low <= {order.price:.2f} 的时间点')
                        else:
                            # 卖单：需要high >= 限价
                            matching_rows = future_data[future_data['high'] > order.price]
                            logger.debug(f'    卖单条件：寻找 high >= {order.price:.2f} 的时间点')

                        if not matching_rows.empty:
                            # 返回第一个满足条件的时间点
                            fill_time = matching_rows.index[0]
                            logger.debug(f'    找到成交时间：{fill_time.strftime("%H:%M")}')

                            # 显示成交时的价格信息
                            fill_row = matching_rows.iloc[0]
                            if order.side == OrderSide.BUY:
                                logger.debug(
                                    f'    成交时价格区间：low={fill_row["low"]:.2f}, high={fill_row["high"]:.2f}'
                                )
                            else:
                                logger.debug(
                                    f'    成交时价格区间：low={fill_row["low"]:.2f}, high={fill_row["high"]:.2f}'
                                )
                        else:
                            logger.debug(f'    未找到满足条件的时间点')
                            # 显示价格范围供调试
                            if order.side == OrderSide.BUY:
                                min_low = future_data['low'].min()
                                logger.debug(
                                    f'    未来最低价：{min_low:.2f}，限价：{order.price:.2f}'
                                )
                            else:
                                max_high = future_data['high'].max()
                                logger.debug(
                                    f'    未来最高价：{max_high:.2f}，限价：{order.price:.2f}'
                                )
                except Exception as e:
                    logger.debug(f'    筛选未来数据时出错: {e}')
            if fill_time:
                order.expected_fill_time = fill_time
                logger.debug(f'  预计成交时间: {fill_time.strftime("%H:%M")}')
            else:
                logger.debug(f'  预计当日无法成交')

        except Exception as e:
            logger.error(f'  预计算成交时间失败: {e}')
            import traceback

            traceback.print_exc()

    @staticmethod
    @lru_cache()
    def _get_intraday_minute_data(symbol: str, date_str: str):
        """
        获取当日完整的分钟K线数据

        Args:
            symbol: 股票代码
            date_str: 当前日期, 特意设置为字符串类型, 以防止没有正确读取key

        Returns:
            DataFrame: 当日分钟K线数据
        """
        try:
            start_datetime = f'{date_str} 09:30:00'
            end_datetime = f'{date_str} 15:00:00'

            df = get_kline_data(
                stock_code=symbol,
                start_date=start_datetime,
                end_date=end_datetime,
                period='1m',
                adjust_type='none',
                fields=['open', 'high', 'low', 'close', 'pre_close', 'high_limit', 'low_limit'],
            )

            if df is not None and not df.empty:
                day_open = df.iloc[0]['open']
                day_pre_close = df.iloc[0]['pre_close']
                high_limit = df.iloc[0]['high_limit']
                low_limit = df.iloc[0]['low_limit']

                return {
                    'day_open': day_open,
                    'day_pre_close': day_pre_close,
                    'high_limit': high_limit,
                    'low_limit': low_limit,
                    'minute_kline': df,
                }
            else:
                logger.debug(f'  {symbol} 当日无分钟K线数据')
                return None

        except Exception as e:
            logger.error(f'  获取当日分钟K线数据失败: {e}')
            import traceback

            traceback.print_exc()
        return None

    def execute_pending_limit_orders(self, start_dt: datetime, end_dt: datetime):
        """
        执行限价单（在指定时间区间内检查成交条件，并执行）

        Args:
            start_dt: 开始时间（不含）
            end_dt: 结束时间（含）
        """

        limit_orders = self.order_manager.get_pending_orders(order_type=OrderType.LIMIT)
        if not limit_orders:
            return

        # 处理每个限价单
        executed_orders = []
        for order in limit_orders:
            # 检查是否已预计算成交时间
            if (
                order.expected_fill_time is not None
                and start_dt < order.expected_fill_time <= end_dt
            ):
                logger.info(
                    f'限价单 {order.order_id[:8]} 在预计时间 {order.expected_fill_time.strftime("%H:%M")} 成交'
                )

                # 执行订单，使用委托价格作为成交价格
                execution_price = order.price
                self._execute_single_order(order, execution_price)
                executed_orders.append(order.order_id)
                logger.info(
                    f'被动成交: {order.order_id[:8]} {order.order_type.name} {order.side.name} {order.symbol} {order.filled_volume}股 {execution_price:.2f}元'
                )

        if executed_orders:
            logger.debug(f'执行了 {len(executed_orders)} 个限价单')

    def _release_frozen_resources(self, order):
        """
        释放订单冻结的资源

        Args:
            order: 订单对象
        """
        if order.frozen_cash > 0:
            self.unlock_cash(order.frozen_cash)
            order.frozen_cash = 0.0
        if order.frozen_volume > 0:
            self.unlock_position(order.symbol, order.frozen_volume)
            order.frozen_volume = 0.0


class BacktestPortfolio(Portfolio):
    def record_daily_value(self, timestamp: datetime, current_prices: Dict[str, float]):
        """
        记录每日投资组合价值到历史数据

        Args:
            timestamp: 当前时间
            current_prices: 当前价格字典
        """
        # 更新持仓价格
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                position.update_price(current_prices[symbol])

        # 记录每日持仓快照
        daily_positions = []
        for symbol, position in self.positions.items():
            if position.total_volume > 0:  # 只记录有持仓的股票
                daily_positions.append(
                    {
                        'symbol': symbol,
                        'total_volume': position.total_volume,
                        'closeable_volume': position.closeable_volume,
                        'avg_cost': position.avg_cost,
                        'current_price': position.price,
                        'market_value': position.value,
                        'pnl': (position.price - position.avg_cost) * position.total_volume,
                        'pnl_pct': ((position.price - position.avg_cost) / position.avg_cost)
                        if position.avg_cost > 0
                        else 0.0,
                    }
                )

        # 记录历史数据
        self.history['timestamp'].append(timestamp)
        self.history['total_value'].append(self.total_value)
        self.history['available_cash'].append(self.available_cash)
        self.history['market_value'].append(self.market_value)
        self.history['positions'].append(daily_positions)


class SimulationPortfolio(Portfolio):
    pass


class LivePortfolio(Portfolio):
    pass
