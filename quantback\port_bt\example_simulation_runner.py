"""
模拟盘运行示例
"""

import logging
import time
from datetime import datetime

from quantback.port_bt.engine import SimulationEngine
from quantback.port_bt.strategies.example_strategy import ExampleStrategy

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def status_callback(status: str, data: dict):
    """状态回调函数"""
    logger.info(f'状态变化: {status}, 数据: {data}')


def main():
    """主函数"""
    
    # 策略配置
    strategy_config = {
        'initial_cash': 1000000.0,  # 初始资金100万
    }
    
    try:
        # 创建模拟盘引擎
        logger.info('正在创建模拟盘引擎...')
        engine = SimulationEngine(
            strategy=ExampleStrategy,
            strategy_config=strategy_config,
            status_callback=status_callback
        )
        
        # 启动模拟盘
        logger.info('启动模拟盘...')
        engine.start()
        
        # 运行一段时间，定期打印状态
        logger.info('模拟盘运行中，按 Ctrl+C 停止...')
        
        try:
            while True:
                time.sleep(30)  # 每30秒打印一次状态
                
                # 获取投资组合状态
                portfolio_status = engine.get_portfolio_status()
                
                logger.info('=' * 60)
                logger.info('投资组合状态:')
                logger.info(f'  可用资金: {portfolio_status.get("available_cash", 0):,.2f}')
                logger.info(f'  冻结资金: {portfolio_status.get("locked_cash", 0):,.2f}')
                logger.info(f'  持仓市值: {portfolio_status.get("market_value", 0):,.2f}')
                logger.info(f'  总资产:   {portfolio_status.get("total_value", 0):,.2f}')
                logger.info(f'  运行状态: {portfolio_status.get("is_running", False)}')
                logger.info(f'  当前时间: {portfolio_status.get("current_time", "N/A")}')
                
                # 打印持仓信息
                positions = portfolio_status.get('positions', {})
                if positions:
                    logger.info('  持仓详情:')
                    for symbol, pos in positions.items():
                        logger.info(f'    {symbol}: {pos["total_volume"]}股, '
                                  f'成本{pos["avg_cost"]:.2f}, '
                                  f'现价{pos["current_price"]:.2f}, '
                                  f'盈亏{pos["pnl"]:.2f}({pos["pnl_pct"]:.2%})')
                else:
                    logger.info('  当前无持仓')
                
                logger.info('=' * 60)
                
        except KeyboardInterrupt:
            logger.info('收到停止信号...')
            
    except Exception as e:
        logger.error(f'运行出错: {e}')
        
    finally:
        # 停止模拟盘
        if 'engine' in locals():
            logger.info('正在停止模拟盘...')
            engine.stop()
            logger.info('模拟盘已停止')


if __name__ == '__main__':
    main()
