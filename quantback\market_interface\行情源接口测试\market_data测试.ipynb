from quantback.market_interface.market_data import get_kline_data
import pandas as pd

# 测试日线数据
df = get_kline_data(
    '600519.SH',
    '2024-12-01',
    '2025-01-06',
    period='1d',
    adjust_type='pre',
    fields=[
        'open',
        'high',
        'low',
        'close',
        'pre_close',
        'volume',
        'amount',
        'high_limit',
        'low_limit',
    ],
)
df


from quantback.market_interface.market_data import get_realtime_kline_data

# 测试日线数据
df = get_realtime_kline_data(
    '600519.SH',
    '2024-12-01',
    '2025-01-06',
    period='1d',
    adjust_type='pre',
    fields=[
        'open',
        'high',
        'low',
        'close',
        'pre_close',
        'volume',
        'amount',
        'high_limit',
        'low_limit',
    ],
)
df