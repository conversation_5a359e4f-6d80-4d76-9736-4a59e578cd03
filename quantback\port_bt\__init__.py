"""
事件驱动的股票量化回测框架
面向对象的Strategy类设计，使用ClickHouse数据源
"""

from .engine import BacktestEngine, SimulationEngine
from .portfolio import OrderCost, Portfolio
from .statistics import Statistics
from .strategy import Strategy
from .visualization import Visualizer

__version__ = '2.0.0'
__all__ = [
    'BacktestEngine',
    'SimulationEngine',
    'OrderCost',
    'Portfolio',
    'Statistics',
    'Strategy',
    'Visualizer',
]
