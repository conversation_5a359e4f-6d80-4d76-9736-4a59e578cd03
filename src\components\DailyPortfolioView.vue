<template>
  <div class="card bg-base-100 border">
    <div class="card-body">
      <div v-if="!hasData">
        <h2 class="card-title">每日资产与持仓</h2>
        <p class="text-gray-500">暂无数据</p>
      </div>
      <div v-else>
        <div class="flex justify-between items-center mb-4">
          <h2 class="card-title">每日资产与持仓明细</h2>
          <div class="text-sm text-gray-500">共 {{ dailyData.length }} 个交易日</div>
        </div>

        <!-- 日期筛选 -->
        <div class="mb-4">
          <div class="form-control w-full max-w-xs">
            <label class="label">
              <span class="label-text">选择日期</span>
            </label>
            <select class="select select-bordered" v-model="selectedDateIndex">
              <option v-for="(item, index) in dailyData" :key="index" :value="index">
                {{ item.date }} - 总资产: {{ formatCurrency(item.total_value) }}
              </option>
            </select>
          </div>
        </div>

        <!-- 选中日期的详细信息 -->
        <div v-if="selectedDayData" class="space-y-4">
          <!-- 资产汇总 -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="stat bg-base-200 rounded-lg">
              <div class="stat-title">总资产</div>
              <div class="stat-value text-xl text-primary">
                {{ formatCurrency(selectedDayData.total_value) }}
              </div>
              <div class="stat-desc">
                <span :class="getReturnColor(selectedDayData.returns)">
                  当日收益: {{ formatPercent(selectedDayData.returns) }}
                </span>
              </div>
            </div>

            <div class="stat bg-base-200 rounded-lg">
              <div class="stat-title">可用资金</div>
              <div class="stat-value text-lg">
                {{ formatCurrency(selectedDayData.available_cash) }}
              </div>
              <div class="stat-desc">
                占比:
                {{
                  ((selectedDayData.available_cash / selectedDayData.total_value) * 100).toFixed(2)
                }}%
              </div>
            </div>

            <div class="stat bg-base-200 rounded-lg">
              <div class="stat-title">持仓市值</div>
              <div class="stat-value text-lg">
                {{ formatCurrency(selectedDayData.market_value) }}
              </div>
              <div class="stat-desc">
                占比:
                {{
                  ((selectedDayData.market_value / selectedDayData.total_value) * 100).toFixed(2)
                }}%
              </div>
            </div>

            <div class="stat bg-base-200 rounded-lg">
              <div class="stat-title">累积收益率</div>
              <div
                class="stat-value text-lg"
                :class="getReturnColor(selectedDayData.cumulative_returns)"
              >
                {{ formatPercent(selectedDayData.cumulative_returns) }}
              </div>
              <div class="stat-desc">自回测开始</div>
            </div>
          </div>

          <!-- 持仓明细 -->
          <div v-if="selectedDayData.positions && selectedDayData.positions.length > 0">
            <h3 class="text-lg font-semibold mb-3">当日持仓明细</h3>
            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>股票代码</th>
                    <th>总持仓</th>
                    <th>可卖持仓</th>
                    <th>平均成本</th>
                    <th>当前价格</th>
                    <th>市值</th>
                    <th>盈亏金额</th>
                    <th>盈亏比例</th>
                    <th>占比</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(position, index) in selectedDayData.positions" :key="index">
                    <td class="font-mono font-semibold">{{ position.symbol }}</td>
                    <td class="font-mono">{{ formatNumber(position.total_volume) }}</td>
                    <td class="font-mono">{{ formatNumber(position.closeable_volume) }}</td>
                    <td class="font-mono">{{ formatCurrency(position.avg_cost) }}</td>
                    <td class="font-mono">{{ formatCurrency(position.current_price) }}</td>
                    <td class="font-mono font-semibold">
                      {{ formatCurrency(position.market_value) }}
                    </td>
                    <td
                      class="font-mono"
                      :class="getReturnColor(position.pnl / Math.abs(position.pnl || 1))"
                    >
                      {{ formatCurrency(position.pnl) }}
                    </td>
                    <td class="font-mono" :class="getReturnColor(position.pnl_pct)">
                      {{ formatPercent(position.pnl_pct) }}
                    </td>
                    <td class="font-mono">
                      <div class="flex items-center space-x-2">
                        <span>{{ getPositionPercentage(position.market_value).toFixed(2) }}%</span>
                        <progress
                          class="progress progress-primary w-16"
                          :value="getPositionPercentage(position.market_value)"
                          max="100"
                        ></progress>
                      </div>
                    </td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr class="font-semibold">
                    <td>合计</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td class="font-mono">{{ formatCurrency(selectedDayData.market_value) }}</td>
                    <td
                      class="font-mono"
                      :class="getReturnColor(totalPnl / Math.abs(totalPnl || 1))"
                    >
                      {{ formatCurrency(totalPnl) }}
                    </td>
                    <td>-</td>
                    <td>100.00%</td>
                  </tr>
                </tfoot>
              </table>
            </div>

            <!-- 持仓统计 -->
            <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="stat bg-base-200 rounded-lg">
                <div class="stat-title">持仓品种数</div>
                <div class="stat-value text-2xl">{{ selectedDayData.positions.length }}</div>
              </div>

              <div class="stat bg-base-200 rounded-lg">
                <div class="stat-title">平均持仓市值</div>
                <div class="stat-value text-xl">
                  {{
                    formatCurrency(selectedDayData.market_value / selectedDayData.positions.length)
                  }}
                </div>
              </div>

              <div class="stat bg-base-200 rounded-lg">
                <div class="stat-title">总盈亏</div>
                <div
                  class="stat-value text-xl"
                  :class="getReturnColor(totalPnl / Math.abs(totalPnl || 1))"
                >
                  {{ formatCurrency(totalPnl) }}
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <h3 class="text-lg font-semibold mb-3">当日持仓明细</h3>
            <div class="text-center py-8">
              <p class="text-gray-500">当日无持仓</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  portfolioHistory: {
    type: Object,
    default: null,
  },
})

const selectedDateIndex = ref(0)

const hasData = computed(() => {
  return (
    props.portfolioHistory &&
    props.portfolioHistory.timestamp &&
    props.portfolioHistory.timestamp.length > 0
  )
})

const dailyData = computed(() => {
  if (!hasData.value) return []

  const { timestamp, total_value, available_cash, market_value, positions } = props.portfolioHistory

  // 计算初始值用于累积收益率计算
  const initialValue = total_value[0] || 1

  return timestamp
    .map((ts, index) => {
      // 计算当日收益率
      const currentValue = total_value[index] || 0
      const previousValue = index > 0 ? total_value[index - 1] : currentValue
      const returns = previousValue > 0 ? (currentValue - previousValue) / previousValue : 0

      // 计算累积收益率
      const cumulative_returns = initialValue > 0 ? (currentValue - initialValue) / initialValue : 0

      // 使用本地时区格式化日期
      const date = new Date(ts).toLocaleDateString()

      return {
        date,
        total_value: currentValue,
        available_cash: available_cash[index] || 0,
        market_value: market_value[index] || 0,
        positions: positions[index] || [],
        returns,
        cumulative_returns,
      }
    })
    .reverse() // 最新日期在前
})

const selectedDayData = computed(() => {
  if (dailyData.value.length === 0) return null
  return dailyData.value[selectedDateIndex.value]
})

const totalPnl = computed(() => {
  if (!selectedDayData.value || !selectedDayData.value.positions) return 0
  return selectedDayData.value.positions.reduce((sum, pos) => sum + (pos.pnl || 0), 0)
})

const formatCurrency = (value) => {
  return `¥${value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

const formatNumber = (value) => {
  return value.toLocaleString('zh-CN')
}

const formatPercent = (value) => {
  return `${(value * 100).toFixed(2)}%`
}

const getReturnColor = (value) => {
  if (value > 0) return 'text-success'
  if (value < 0) return 'text-error'
  return 'text-gray-500'
}

const getPositionPercentage = (marketValue) => {
  if (!selectedDayData.value) return 0
  return selectedDayData.value.market_value > 0
    ? (marketValue / selectedDayData.value.market_value) * 100
    : 0
}
</script>
