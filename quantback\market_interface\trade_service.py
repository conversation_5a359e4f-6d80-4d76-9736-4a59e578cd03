import atexit
import logging
import os
import sqlite3
import threading
import time
from pathlib import Path
from typing import Dict, Optional

from xtquant import xtconstant, xtdata
from xtquant import xtpythonclient as _XTQC_
from xtquant import xttype as _XTTYPE_
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount

import quantback
from quantback.port_bt.portfolio import Position
from quantback.utils import SingletonMeta

logger = logging.getLogger(__name__)


class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def __init__(self, xtService):
        self.xtService = xtService

    def on_disconnected(self):
        """
        连接断开
        :return:
        """
        logger.info('connection lost')

    def on_stock_order(self, order):
        """
        委托回报推送
        :param order: XtOrder对象
        :return:
        """
        logger.info('on_stock_order')
        logger.info(
            f'stock_code:{order.stock_code} order_status:{order.order_status} order_sysid:{order.order_sysid}'
        )

    def on_stock_asset(self, asset):
        """
        资金变动推送
        :param asset: XtAsset对象
        :return:
        """
        logger.info('on_stock_asset')
        logger.info(f'账号:{asset.account_id} cash:{asset.cash} total_asset:{asset.total_asset}')

    def on_stock_trade(self, trade):
        """
        成交变动推送
        :param trade: XtTrade对象
        :return:
        """
        logger.info('on_stock_trade')
        logger.info(
            f'账号:{trade.account_id} stock_code:{trade.stock_code} traded_volume:{trade.traded_volume} traded_price:{trade.traded_price}'
        )

        # 更新策略持仓
        strategy_name = trade.strategy_name or '未指定'
        if strategy_name not in self.xtService.strategy_positions:
            self.xtService.strategy_positions[strategy_name] = {}
        positions = self.xtService.strategy_positions[strategy_name]
        stock_code = trade.stock_code

        # 如果持仓不存在，创建新的Position对象
        if stock_code not in positions:
            positions[stock_code] = Position(stock_code)

        position = positions[stock_code]

        # 根据买卖方向更新持仓
        volume_change = 0
        traded_price = trade.traded_price if hasattr(trade, 'traded_price') else 0.0

        if trade.order_type == xtconstant.STOCK_BUY:
            # 买入：更新平均成本和持仓数量
            if position.total_volume == 0:
                position.avg_cost = traded_price
            else:
                total_cost = (
                    position.avg_cost * position.total_volume + traded_price * trade.traded_volume
                )
                position.avg_cost = total_cost / (position.total_volume + trade.traded_volume)

            position.total_volume += trade.traded_volume
            position.closeable_volume += trade.traded_volume  # 实盘中当日买入可能当日可卖
            volume_change = trade.traded_volume

        elif trade.order_type == xtconstant.STOCK_SELL:
            # 卖出：减少持仓数量
            position.total_volume -= trade.traded_volume
            position.closeable_volume -= trade.traded_volume
            volume_change = -trade.traded_volume

            # 如果持仓为0，删除该条记录
            if position.total_volume == 0:
                del positions[stock_code]
                if not positions:  # 如果策略没有任何持仓，删除策略记录
                    del self.xtService.strategy_positions[strategy_name]

        # 记录交易到SQLite数据库
        self.xtService._update_position_in_db(
            strategy_name, stock_code, position if volume_change != 0 else None
        )

    def on_stock_position(self, position):
        """
        持仓变动推送
        :param position: XtPosition对象
        :return:
        """
        logger.info('on_stock_position')
        logger.info(f'stock_code:{position.stock_code} volume:{position.volume}')

    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        logger.info('on_order_error')
        logger.info(
            f'order_id:{order_error.order_id} error_id:{order_error.error_id} error_msg:{order_error.error_msg}'
        )

    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        logger.info('on_cancel_error')
        logger.info(
            f'order_id:{cancel_error.order_id} error_id:{cancel_error.error_id} error_msg:{cancel_error.error_msg}'
        )

    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        logger.info('on_order_stock_async_response')
        logger.info(f'账号:{response.account_id} order_id:{response.order_id} seq:{response.seq}')

    def on_cancel_order_stock_async_response(self, response):
        """
        异步撤单回报推送
        :param response: XtCancelOrderResponse 对象
        :return:
        """
        logger.info('on_cancel_order_stock_async_response')
        logger.info(f'账号:{response.account_id} order_id:{response.order_id} seq:{response.seq}')

    def on_account_status(self, status):
        """
        :param response: XtAccountStatus 对象
        :return:
        """
        logger.info('账号状态发生变化')
        logger.info(f'账号:{status.account_id} 类型:{status.account_type} 状态:{status.status}')


class MyXtQuantTrader(XtQuantTrader):
    """
    去除asyncio的代码。如果xtquant升级了，将下面代码与新父类对比一下。
    """

    def __init__(self, path, session, callback=None):
        """
        :param path: mini版迅投极速交易客户端安装路径下，userdata文件夹具体路径
        :param session: 当前任务执行所属的会话id
        :param callback: 回调方法
        """

        self.async_client = _XTQC_.XtQuantAsyncClient(path.encode('gb18030'), 'xtquant', session)
        self.callback = callback

        self.connected = False

        self.cbs = {}

        self.executor = None
        self.resp_executor = None

        self.relaxed_resp_order_enabled = False
        self.relaxed_resp_executor = None

        self.queuing_order_seq = set()  # 发起委托的seq,获取resp时移除
        self.handled_async_order_stock_order_id = set()  # 已处理了返回的委托order_id
        # 队列中的委托失败信息，在对应委托尚未返回(检测seq或者order_id)时存入，等待回调error_callback
        self.queuing_order_errors_byseq = {}
        self.queuing_order_errors_byid = {}

        self.handled_async_cancel_order_stock_order_id = set()
        self.handled_async_cancel_order_stock_order_sys_id = set()
        self.queuing_cancel_errors_by_order_id = {}
        self.queuing_cancel_errors_by_order_sys_id = {}

        #########################
        # push
        def on_common_push_callback_wrapper(argc, callback):
            if argc == 0:

                def on_push_data():
                    self.executor.submit(callback)

                return on_push_data
            elif argc == 1:

                def on_push_data(data):
                    self.executor.submit(callback, data)

                return on_push_data
            elif argc == 2:

                def on_push_data(data1, data2):
                    self.executor.submit(callback, data1, data2)

                return on_push_data
            else:
                return None

        # response
        def on_common_resp_callback(seq, resp):
            callback = self.cbs.pop(seq, None)
            if callback:
                self.resp_executor.submit(callback, resp)
            return

        self.async_client.bindOnSubscribeRespCallback(on_common_resp_callback)
        self.async_client.bindOnUnsubscribeRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStockAssetCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStockOrdersCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStockTradesCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStockPositionsCallback(on_common_resp_callback)
        self.async_client.bindOnQueryCreditDetailRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryStkCompactsRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryCreditSubjectsRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryCreditSloCodeRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryCreditAssureRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryNewPurchaseLimitCallback(on_common_resp_callback)
        self.async_client.bindOnQueryIPODataCallback(on_common_resp_callback)
        self.async_client.bindOnTransferRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryComFundRespCallback(on_common_resp_callback)
        self.async_client.bindOnSmtQueryQuoterRespCallback(on_common_resp_callback)
        self.async_client.bindOnSmtQueryOrderRespCallback(on_common_resp_callback)
        self.async_client.bindOnSmtQueryCompactRespCallback(on_common_resp_callback)
        self.async_client.bindOnQueryPositionStatisticsRespCallback(on_common_resp_callback)
        self.async_client.bindOnExportDataRespCallback(on_common_resp_callback)
        self.async_client.bindOnSyncTransactionFromExternalRespCallback(on_common_resp_callback)

        self.async_client.bindOnQueryAccountInfosCallback(on_common_resp_callback)
        self.async_client.bindOnQueryAccountStatusCallback(on_common_resp_callback)
        #########################

        enable_push = 1

        # order push

        def on_push_OrderStockAsyncResponse(seq, resp):
            callback = self.cbs.pop(seq, None)
            if callback:
                resp = _XTTYPE_.XtOrderResponse(
                    resp.m_strAccountID,
                    resp.m_nOrderID,
                    resp.m_strStrategyName,
                    resp.m_strOrderRemark,
                    resp.m_strErrorMsg,
                    seq,
                )
                callback(resp)
                self.queuing_order_seq.discard(seq)
                e = self.queuing_order_errors_byseq.pop(seq, None)
                if not e:
                    e = self.queuing_order_errors_byid.pop(resp.order_id, None)
                if e is not None:
                    self.callback.on_order_error(e)
                else:
                    self.handled_async_order_stock_order_id.add(resp.order_id)
            return

        if enable_push:
            self.async_client.bindOnOrderStockRespCallback(
                on_common_push_callback_wrapper(2, on_push_OrderStockAsyncResponse)
            )

        def on_push_CancelOrderStockAsyncResponse(seq, resp):
            callback = self.cbs.pop(seq, None)
            if callback:
                resp = _XTTYPE_.XtCancelOrderResponse(
                    resp.m_strAccountID,
                    resp.m_nCancelResult,
                    resp.m_nOrderID,
                    resp.m_strOrderSysID,
                    seq,
                    resp.m_strErrorMsg,
                )
                callback(resp)

                if not resp.order_sysid:
                    e = self.queuing_cancel_errors_by_order_id.pop(resp.order_id, None)
                    if e is not None:
                        self.handled_async_cancel_order_stock_order_id.discard(resp.order_id)
                        self.callback.on_cancel_error(e)
                    else:
                        self.handled_async_cancel_order_stock_order_id.add(resp.order_id)
                else:
                    e = self.queuing_cancel_errors_by_order_sys_id.pop(resp.order_sysid, None)
                    if e is not None:
                        self.handled_async_cancel_order_stock_order_sys_id.discard(resp.order_sysid)
                        self.callback.on_cancel_error(e)
                    else:
                        self.handled_async_cancel_order_stock_order_sys_id.add(resp.order_sysid)
            return

        if enable_push:
            self.async_client.bindOnCancelOrderStockRespCallback(
                on_common_push_callback_wrapper(2, on_push_CancelOrderStockAsyncResponse)
            )

        def on_push_disconnected():
            if self.callback:
                self.callback.on_disconnected()

        if enable_push:
            self.async_client.bindOnDisconnectedCallback(
                on_common_push_callback_wrapper(0, on_push_disconnected)
            )

        def on_push_AccountStatus(data):
            data = _XTTYPE_.XtAccountStatus(
                data.m_strAccountID, data.m_nAccountType, data.m_nStatus
            )
            self.callback.on_account_status(data)

        if enable_push:
            self.async_client.bindOnUpdateAccountStatusCallback(
                on_common_push_callback_wrapper(1, on_push_AccountStatus)
            )

        def on_push_StockAsset(data):
            self.callback.on_stock_asset(data)

        if enable_push:
            self.async_client.bindOnStockAssetCallback(
                on_common_push_callback_wrapper(1, on_push_StockAsset)
            )

        def on_push_OrderStock(data):
            self.callback.on_stock_order(data)

        if enable_push:
            self.async_client.bindOnStockOrderCallback(
                on_common_push_callback_wrapper(1, on_push_OrderStock)
            )

        def on_push_StockTrade(data):
            self.callback.on_stock_trade(data)

        if enable_push:
            self.async_client.bindOnStockTradeCallback(
                on_common_push_callback_wrapper(1, on_push_StockTrade)
            )

        def on_push_StockPosition(data):
            self.callback.on_stock_position(data)

        if enable_push:
            self.async_client.bindOnStockPositionCallback(
                on_common_push_callback_wrapper(1, on_push_StockPosition)
            )

        def on_push_OrderError(data):
            if (
                data.seq not in self.queuing_order_seq
                or data.order_id in self.handled_async_order_stock_order_id
            ):
                self.handled_async_order_stock_order_id.discard(data.order_id)
                self.callback.on_order_error(data)
            else:
                self.queuing_order_errors_byseq[data.seq] = data
                self.queuing_order_errors_byid[data.order_id] = data

        if enable_push:
            self.async_client.bindOnOrderErrorCallback(
                on_common_push_callback_wrapper(1, on_push_OrderError)
            )

        def on_push_CancelError(data):
            if data.order_id in self.handled_async_cancel_order_stock_order_id:
                self.handled_async_cancel_order_stock_order_id.discard(data.order_id)
                self.callback.on_cancel_error(data)
            elif data.order_sysid in self.handled_async_cancel_order_stock_order_sys_id:
                self.handled_async_cancel_order_stock_order_sys_id.discard(data.order_sysid)
                self.callback.on_cancel_error(data)
            else:
                self.queuing_cancel_errors_by_order_id[data.order_id] = data
                self.queuing_cancel_errors_by_order_sys_id[data.order_sysid] = data

        if enable_push:
            self.async_client.bindOnCancelErrorCallback(
                on_common_push_callback_wrapper(1, on_push_CancelError)
            )

        def on_push_SmtAppointmentAsyncResponse(seq, resp):
            callback = self.cbs.pop(seq, None)
            if callback:
                resp = _XTTYPE_.XtSmtAppointmentResponse(
                    seq, resp.m_bSuccess, resp.m_strMsg, resp.m_strApplyID
                )
                callback(resp)
            return

        if enable_push:
            self.async_client.bindOnSmtAppointmentRespCallback(
                on_common_push_callback_wrapper(2, on_push_SmtAppointmentAsyncResponse)
            )

        # 判断并存储运行模式
        self.is_test_mode = os.getenv('MODE') == 'test'

    def order_stock_async(
        self,
        account,
        stock_code,
        order_type,
        order_volume,
        price_type,
        price,
        strategy_name='',
        order_remark='',
    ):
        # 将来如果短时间下很多单, 需要使用utils.py的rate_limit装饰器限流.
        logger.info(
            f'下单参数: 账户id={account.account_id}, 股票代码={stock_code}, 订单类型={order_type}, 数量={order_volume}, 价格类型={price_type}, 价格={price}, 策略名称={strategy_name}, 备注={order_remark}'
        )
        if self.is_test_mode:
            logger.info('模拟下单')
            seq = int(time.time_ns())  # 生成一个模拟的订单序列号
        else:
            seq = super().order_stock_async(
                account,
                stock_code,
                order_type,
                order_volume,
                price_type,
                price,
                strategy_name,
                order_remark,
            )
        return seq

    def stop(self):
        self.async_client.stop()
        self.executor.shutdown(wait=False)
        self.relaxed_resp_executor.shutdown(wait=False)

    def __del__(self):
        pass


class TradeService(metaclass=SingletonMeta):
    def __init__(self):
        self.xt_trader = None
        config = quantback.get_config()
        self.miniqmt_dir = config.get('miniqmt.dir')
        logger.debug(f'使用配置的路径: {self.miniqmt_dir}')

        self.active_profile = str(config.get('miniqmt.active_profile'))
        logger.debug(f'当前激活的配置文件: {self.active_profile}')

        self.account_id = config.get(f'miniqmt.profiles.{self.active_profile}.account')
        self.account_type = config.get(f'miniqmt.profiles.{self.active_profile}.type')
        logger.debug(f'账户id: {self.account_id}, 类型: {self.account_type}')

        self.stock_account = StockAccount(self.account_id, account_type=self.account_type)
        self.callback = MyXtQuantTraderCallback(self)
        self.connect()

        # SQLite数据库路径
        self.data_dir = Path(__file__).parent
        self.db_file = self.data_dir / 'strategy_positions.db'

        # SQLite连接和锁
        self._db_lock = threading.RLock()
        self._db_conn = None
        self._init_sqlite_db()

        # 初始化策略持仓（内存缓存）
        self.strategy_positions = {}
        self._load_strategy_positions()

        # 注册程序退出时的清理函数
        atexit.register(self._cleanup_on_exit)

    def _init_sqlite_db(self):
        """初始化SQLite数据库和表结构"""
        try:
            self._db_conn = sqlite3.connect(
                str(self.db_file), check_same_thread=False, timeout=30.0
            )

            # 启用WAL模式和性能优化
            self._db_conn.execute('PRAGMA journal_mode=WAL')
            self._db_conn.execute('PRAGMA synchronous=NORMAL')  # 平衡性能和安全性
            self._db_conn.execute('PRAGMA cache_size=10000')  # 10MB缓存
            self._db_conn.execute('PRAGMA temp_store=MEMORY')  # 临时表存储在内存
            self._db_conn.execute('PRAGMA mmap_size=268435456')  # 256MB内存映射

            # 创建策略持仓表
            self._db_conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_positions (
                    strategy_name TEXT NOT NULL,
                    stock_code TEXT NOT NULL,
                    total_volume INTEGER NOT NULL DEFAULT 0,
                    closeable_volume INTEGER NOT NULL DEFAULT 0,
                    avg_cost REAL NOT NULL DEFAULT 0.0,
                    price REAL NOT NULL DEFAULT 0.0,
                    value REAL NOT NULL DEFAULT 0.0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (strategy_name, stock_code)
                )
            """)

            # 创建索引优化查询性能
            self._db_conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_positions_strategy
                ON strategy_positions(strategy_name)
            """)

            self._db_conn.commit()
            logger.info(f'SQLite数据库初始化完成: {self.db_file}')

        except Exception as e:
            logger.error(f'初始化SQLite数据库失败: {str(e)}')
            raise

    def _cleanup_on_exit(self):
        """程序退出时清理资源"""
        logger.info('程序退出，清理SQLite连接...')
        try:
            if self._db_conn:
                self._db_conn.close()
                logger.info('SQLite连接已关闭')
        except Exception as e:
            logger.error(f'关闭SQLite连接失败: {str(e)}')

    def connect(self):
        """连接到迅投交易接口"""
        # 关闭之前的xt_trader
        if self.xt_trader:
            self.xt_trader.stop()
        session_id = int(time.time())
        self.xt_trader = MyXtQuantTrader(self.miniqmt_dir, session_id)  # 重连时必须更新session_id
        self.xt_trader.set_relaxed_response_order_enabled(
            True
        )  # qmt的交易接口push和response各用一个线程。不是qmt行情接口的push。
        self.xt_trader.register_callback(self.callback)
        self.xt_trader.start()

        connect_result = self.xt_trader.connect()
        if connect_result == 0:
            logger.info('连接成功')
            subscribe_result = self.xt_trader.subscribe(self.stock_account)
            if subscribe_result == 0:
                logger.info('账户订阅成功')
            else:
                logger.error('账户订阅失败')
            return True
        else:
            logger.error(f'连接失败:{connect_result}')
            return False

    def disconnect(self):
        """断开连接并关闭资源"""
        # 关闭SQLite连接
        try:
            if self._db_conn:
                self._db_conn.close()
                logger.info('SQLite连接已关闭')
        except Exception as e:
            logger.error(f'关闭SQLite连接失败: {str(e)}')
        # 断开交易连接
        self.xt_trader.stop()

    def order(self, stock_code, volume, strategy_name, round_to_100=True, price=None):
        """
        下单
        清仓可以使用order_target(volume=0), 允许包含碎股数量.
        """
        if not price:
            tick = xtdata.get_full_tick([stock_code])[stock_code]
            price = tick['lastPrice']
        price_type = xtconstant.FIX_PRICE

        if round_to_100:
            # 科创板下单数量要>=200, 否则数量改为0; 可交易200股以上的零散股, 不用取整到100的整数倍
            if stock_code.startswith('688'):
                if volume < 200:
                    logger.warning(f'科创板下单数量小于200: {volume}')
                    volume = 0
            elif volume % 100 != 0:
                logger.warning(f'下单数量不是100的整数倍: {volume}')
                volume = int(volume / 100) * 100
        if volume > 0:
            order_type = xtconstant.STOCK_BUY
            price = max(price + 0.01, price * 1.0015)
        elif volume < 0:
            order_type = xtconstant.STOCK_SELL
            price = min(price - 0.01, price * 0.9985)
        else:
            return 0
        volume = abs(volume)

        return self.xt_trader.order_stock_async(
            self.stock_account, stock_code, order_type, volume, price_type, price, strategy_name, ''
        )

    def order_target(self, stock_code, volume, strategy_name, price=None):
        """下单到目标持仓数量"""
        current_position = self.strategy_positions.get(strategy_name, {}).get(stock_code)
        current_volume = current_position.total_volume if current_position else 0
        logger.info(
            f'股票代码: {stock_code}, 当前持仓数量: {current_volume}, 目标持仓数量: {volume}'
        )
        round_to_100 = volume != 0
        return self.order(
            stock_code, volume - current_volume, strategy_name, round_to_100, price=price
        )

    def order_value(self, stock_code, value, strategy_name, price=None):
        """下单value市值, 如果value为负数, 则卖出"""
        if not price:
            tick = xtdata.get_full_tick([stock_code])[stock_code]
            price = tick['lastPrice']
        volume = value / price
        return self.order(stock_code, volume, strategy_name, price=price)

    def order_target_value(self, stock_code, value, strategy_name, price=None):
        """下单到目标持仓市值"""
        if not price:
            tick = xtdata.get_full_tick([stock_code])[stock_code]
            price = tick['lastPrice']
        volume = value / price
        return self.order_target(stock_code, volume, strategy_name, price=price)

    def _load_strategy_positions(self):
        """从SQLite数据库加载策略持仓数据到内存缓存"""
        try:
            with self._db_lock:
                cursor = self._db_conn.execute("""
                    SELECT strategy_name, stock_code, total_volume, closeable_volume, avg_cost, price, value
                    FROM strategy_positions
                    WHERE total_volume != 0
                """)

                self.strategy_positions = {}
                for (
                    strategy_name,
                    stock_code,
                    total_volume,
                    closeable_volume,
                    avg_cost,
                    price,
                    value,
                ) in cursor.fetchall():
                    if strategy_name not in self.strategy_positions:
                        self.strategy_positions[strategy_name] = {}

                    # 创建Position对象并设置属性
                    position = Position(stock_code)
                    position.total_volume = total_volume
                    position.closeable_volume = closeable_volume
                    position.avg_cost = avg_cost
                    position.price = price
                    position.value = value

                    self.strategy_positions[strategy_name][stock_code] = position

                total_positions = sum(
                    len(positions) for positions in self.strategy_positions.values()
                )
                logger.info(
                    f'从SQLite数据库加载了{len(self.strategy_positions)}个策略的{total_positions}个持仓记录'
                )

        except Exception as e:
            logger.error(f'从SQLite数据库加载策略持仓数据时出错: {str(e)}')
            self.strategy_positions = {}  # 初始化空持仓

    def _update_position_in_db(
        self, strategy_name: str, stock_code: str, position: Optional[Position]
    ):
        """更新策略持仓到SQLite数据库"""
        try:
            with self._db_lock:
                # 开始事务
                self._db_conn.execute('BEGIN IMMEDIATE')

                try:
                    if position is None:
                        # 持仓为0，删除记录
                        self._db_conn.execute(
                            """
                            DELETE FROM strategy_positions
                            WHERE strategy_name = ? AND stock_code = ?
                        """,
                            (strategy_name, stock_code),
                        )
                    else:
                        # 更新或插入持仓记录
                        self._db_conn.execute(
                            """
                            INSERT OR REPLACE INTO strategy_positions
                            (strategy_name, stock_code, total_volume, closeable_volume, avg_cost, price, value, last_updated)
                            VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                        """,
                            (
                                strategy_name,
                                stock_code,
                                position.total_volume,
                                position.closeable_volume,
                                position.avg_cost,
                                position.price,
                                position.value,
                            ),
                        )

                    # 提交事务
                    self._db_conn.commit()

                except Exception as e:
                    # 回滚事务
                    self._db_conn.rollback()
                    raise e

        except Exception as e:
            logger.error(f'更新SQLite数据库时出错: {str(e)}')
            # 数据库更新失败不影响内存状态，因为内存已经在交易回调中更新了

    def get_strategy_positions_from_db(self, strategy_name: Optional[str] = None) -> Dict:
        """从数据库查询策略持仓，返回Position对象"""
        try:
            with self._db_lock:
                if strategy_name:
                    cursor = self._db_conn.execute(
                        """
                        SELECT stock_code, total_volume, closeable_volume, avg_cost, price, value, last_updated
                        FROM strategy_positions
                        WHERE strategy_name = ? AND total_volume != 0
                        ORDER BY stock_code
                    """,
                        (strategy_name,),
                    )

                    positions = {}
                    for (
                        stock_code,
                        total_volume,
                        closeable_volume,
                        avg_cost,
                        price,
                        value,
                        last_updated,
                    ) in cursor.fetchall():
                        position = Position(stock_code)
                        position.total_volume = total_volume
                        position.closeable_volume = closeable_volume
                        position.avg_cost = avg_cost
                        position.price = price
                        position.value = value
                        positions[stock_code] = position
                    return {strategy_name: positions}
                else:
                    cursor = self._db_conn.execute("""
                        SELECT strategy_name, stock_code, total_volume, closeable_volume, avg_cost, price, value, last_updated
                        FROM strategy_positions
                        WHERE total_volume != 0
                        ORDER BY strategy_name, stock_code
                    """)

                    all_positions = {}
                    for (
                        strategy_name,
                        stock_code,
                        total_volume,
                        closeable_volume,
                        avg_cost,
                        price,
                        value,
                        last_updated,
                    ) in cursor.fetchall():
                        if strategy_name not in all_positions:
                            all_positions[strategy_name] = {}

                        position = Position(stock_code)
                        position.total_volume = total_volume
                        position.closeable_volume = closeable_volume
                        position.avg_cost = avg_cost
                        position.price = price
                        position.value = value
                        all_positions[strategy_name][stock_code] = position
                    return all_positions

        except Exception as e:
            logger.error(f'查询策略持仓时出错: {str(e)}')
            return {}

    def sync_memory_to_db(self):
        """将内存中的持仓数据同步到数据库（用于数据修复）"""
        try:
            with self._db_lock:
                # 开始事务
                self._db_conn.execute('BEGIN IMMEDIATE')

                try:
                    # 清空现有持仓数据
                    self._db_conn.execute('DELETE FROM strategy_positions')

                    # 插入内存中的持仓数据
                    for strategy_name, positions in self.strategy_positions.items():
                        for stock_code, position in positions.items():
                            self._db_conn.execute(
                                """
                                INSERT INTO strategy_positions
                                (strategy_name, stock_code, total_volume, closeable_volume, avg_cost, price, value, last_updated)
                                VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                            """,
                                (
                                    strategy_name,
                                    stock_code,
                                    position.total_volume,
                                    position.closeable_volume,
                                    position.avg_cost,
                                    position.price,
                                    position.value,
                                ),
                            )

                    self._db_conn.commit()
                    logger.info('内存持仓数据已同步到数据库')

                except Exception as e:
                    self._db_conn.rollback()
                    raise e

        except Exception as e:
            logger.error(f'同步内存数据到数据库时出错: {str(e)}')

    def verify_positions(self):
        """
        获取实际总持仓并验证与策略持仓的一致性
        """

        # 查询这些股票的实际持仓
        total_positions = {}
        positions = self.xt_trader.query_stock_positions(self.stock_account)
        for position in positions:
            if position.stock_code == '888880.SH':
                continue
            total_positions[position.stock_code] = position.volume

        # 验证持仓
        return self.verify_total_positions(total_positions)

    def verify_total_positions(self, total_positions: Dict[str, int]):
        """
        验证策略持仓总和与实际持仓是否一致
        :param total_positions: {stock_code: volume} 实际总持仓
        """
        # 计算所有策略持仓之和
        calculated_positions = {}
        for strategy_positions in self.strategy_positions.values():
            for stock_code, position in strategy_positions.items():
                calculated_positions[stock_code] = (
                    calculated_positions.get(stock_code, 0) + position.total_volume
                )

        # 比对差异
        differences = []
        for stock_code, volume in total_positions.items():
            calc_volume = calculated_positions.get(stock_code, 0)
            if calc_volume != volume:
                differences.append(f'股票{stock_code}: 实际持仓{volume}, 策略持仓和{calc_volume}')

        if differences:
            logger.warning('发现持仓差异:')
            for diff in differences:
                logger.warning(diff)
            return False
        logger.info('总策略持仓与实际持仓一致')
        return True


if __name__ == '__main__':
    pass
