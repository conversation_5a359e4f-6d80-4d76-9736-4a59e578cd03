#!/usr/bin/env python3
"""
量化回测Socket.IO服务器
"""

import asyncio
import logging
import time
import uuid
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiofiles
import aiofiles.os
import pandas as pd
import socketio
import uvicorn

from quantback.log_setup import setup_logging
from quantback.port_bt.engine import BacktestEngine

# 设置日志
logger = logging.getLogger(__name__)

# 创建Socket.IO服务器
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins='*',  # 在生产环境中应该限制具体域名
    # logger=logger,
    engineio_logger=logger,
)
app = socketio.ASGIApp(sio)


# 线程池用于执行耗时的回测任务
executor = ThreadPoolExecutor(max_workers=2)


# 全局状态管理
class BacktestState:
    def __init__(self):
        self.is_running = False
        self.current_strategy = None
        self.start_date = None
        self.end_date = None
        self.initial_cash = None
        self.progress = 0
        self.error_message = None
        self.results = None
        self.task_id = None
        self.realtime_result = None

    async def update_status(self, **kwargs):
        """更新状态并广播给所有Socket.IO连接"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

        # 广播状态更新
        status_message = self.to_dict(include_results=True)
        await sio.emit('status_update', status_message)

    def to_dict(self, include_results=True):
        """转换为字典格式"""
        data = {
            'is_running': self.is_running,
            'current_strategy': self.current_strategy,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'initial_cash': self.initial_cash,
            'progress': self.progress,
            'error_message': self.error_message,
            'task_id': self.task_id,
            'realtime_result': self.realtime_result,
        }

        # 可选择是否包含结果数据（用于优化连接速度）
        if include_results:
            data['results'] = self.results
        else:
            data['results'] = None

        return data


backtest_state = BacktestState()

# 策略目录全局变量
STRATEGIES_DIR = Path(__file__).parent / 'port_bt' / 'strategies'


async def get_strategy_files() -> List[str]:
    """获取策略文件列表（异步版本）- 只返回策略名称"""
    strategy_names = []

    # 使用aiofiles.os检查目录是否存在
    if await aiofiles.os.path.exists(STRATEGIES_DIR):
        # 使用aiofiles.os.listdir异步获取目录内容
        try:
            files = await aiofiles.os.listdir(STRATEGIES_DIR)
            for filename in files:
                if filename.endswith('.py') and filename != '__init__.py':
                    strategy_names.append(Path(filename).stem)
        except OSError as e:
            logger.error(f'读取策略目录失败: {e}')

    return strategy_names


def load_strategy_module(strategy_path: str):
    """动态加载策略模块"""
    import importlib.util

    spec = importlib.util.spec_from_file_location('strategy', strategy_path)
    if spec is None or spec.loader is None:
        raise ValueError(f'无法加载策略文件: {strategy_path}')

    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def run_backtest_sync(
    strategy_path: str,
    start_date: str,
    end_date: str,
    initial_cash: float,
    task_id: str,
    loop: asyncio.AbstractEventLoop,
):
    """同步运行回测（在线程池中执行）"""
    global backtest_state

    def progress_callback(
        current: int,
        total: int,
        timestamp: float,
        total_value: float,
        benchmark_value: Optional[float],
    ):
        """进度回调函数，从线程池安全地调用asyncio事件循环"""
        try:
            # 计算进度百分比
            progress = int((current / total) * 100)

            # 构建实时结果点
            realtime_result = {
                'timestamp': timestamp,
                'total_value': total_value,
                'benchmark_value': benchmark_value,
            }

            # 使用run_coroutine_threadsafe从线程池安全地调用协程
            asyncio.run_coroutine_threadsafe(
                backtest_state.update_status(progress=progress, realtime_result=realtime_result),
                loop,
            )
        except Exception as e:
            logger.warning(f'进度回调执行失败: {e}')

    try:
        # 加载策略模块
        strategy_module = load_strategy_module(strategy_path)

        if not hasattr(strategy_module, 'initialize'):
            raise ValueError('策略文件必须包含initialize函数')

        strategy_config = {
            'start_date': start_date,
            'end_date': end_date,
            'initial_cash': initial_cash,
        }

        # 创建回测引擎，传入进度回调
        engine = BacktestEngine(
            initialize_func=strategy_module.initialize,
            strategy_config=strategy_config,
            progress_callback=progress_callback,
        )

        # 运行回测
        results = engine.run()

        # 处理结果数据，转换为可序列化的格式
        processed_results = process_backtest_results(results)

        # 更新结果到状态（同步更新，异步广播由包装器处理）
        backtest_state.results = processed_results

        logger.info(f'回测完成: {task_id}')
        return processed_results

    except Exception as e:
        logger.error(f'回测执行错误: {e}')
        raise e


def process_backtest_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """处理回测结果，转换为前端可用的格式"""
    try:
        processed = {'performance_stats': results['performance_stats']}

        # 注意timestamp()会假设无时区的datetime处于系统时区, 计算时间戳. 即处于不同系统时区时, 会转换为不同的时间戳.
        # 处理投资组合历史数据
        portfolio_history = results['portfolio_history']
        processed['portfolio_history'] = {
            'timestamp': [ts.timestamp() * 1000 for ts in portfolio_history['timestamp']],
            'total_value': portfolio_history['total_value'],
            'market_value': portfolio_history['market_value'],
            'available_cash': portfolio_history['available_cash'],
            'positions': portfolio_history['positions'],
            'benchmark_data': portfolio_history.get('benchmark_data', None),
        }

        # 处理交易记录
        trade_records = results['trade_records']
        processed['trade_records'] = []
        for record in trade_records:
            # 创建新记录，只转换timestamp字段
            new_record = record.copy()
            new_record['timestamp'] = record['timestamp'].timestamp() * 1000
            processed['trade_records'].append(new_record)

        return processed

    except Exception as e:
        logger.error(f'处理回测结果错误: {e}')
        return {'error': str(e)}


async def run_backtest_in_background(
    strategy_path: str, start_date: str, end_date: str, initial_cash: float, task_id: str
):
    """在后台运行回测"""
    loop = asyncio.get_event_loop()
    try:
        # 广播开始状态
        await backtest_state.update_status(is_running=True, progress=0, error_message=None)

        # 在线程池中执行回测，传入事件循环
        await loop.run_in_executor(
            executor,
            run_backtest_sync,
            strategy_path,
            start_date,
            end_date,
            initial_cash,
            task_id,
            loop,
        )

        # 广播完成状态
        await backtest_state.update_status(is_running=False, progress=100)

    except Exception as e:
        logger.error(f'后台回测任务执行失败: {e}')
        # 广播错误状态
        await backtest_state.update_status(is_running=False, error_message=str(e))


# Socket.IO事件处理器
@sio.event
async def connect(sid, environ=None, auth=None):
    """客户端连接事件"""
    logger.info(f'Socket.IO client connected: {sid}')


@sio.event
async def disconnect(sid):
    """客户端断开连接事件"""
    logger.info(f'Socket.IO client disconnected: {sid}')


@sio.event
async def get_strategies(sid):
    """获取策略列表"""
    try:
        # 使用aiofiles异步获取策略文件列表
        strategies = await get_strategy_files()
        return {'strategies': strategies}
    except Exception as e:
        logger.error(f'获取策略列表错误: {e}')
        return {'error': f'获取策略列表失败: {str(e)}'}


@sio.event
async def start_backtest(sid, data):
    """开始回测"""
    global backtest_state

    try:
        # 检查是否已有回测在运行
        if backtest_state.is_running:
            return {'error': '已有回测任务在运行中'}

        # 验证请求数据
        if not data or not all(
            key in data for key in ['strategy', 'start_date', 'end_date', 'initial_cash']
        ):
            return {'error': '缺少必要参数'}

        strategy = data['strategy']
        start_date = data['start_date']
        end_date = data['end_date']
        initial_cash = data['initial_cash']

        # 验证策略是否存在
        strategies = await get_strategy_files()
        if strategy not in strategies:
            return {'error': f'未找到策略文件: {strategy}'}

        # 根据策略名称拼接完整路径
        strategy_file = str(STRATEGIES_DIR / f'{strategy}.py')

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 更新状态
        backtest_state.current_strategy = strategy
        backtest_state.start_date = start_date
        backtest_state.end_date = end_date
        backtest_state.initial_cash = initial_cash
        backtest_state.task_id = task_id
        backtest_state.results = None  # 清空结果
        backtest_state.error_message = None

        # 在后台执行回测
        asyncio.create_task(
            run_backtest_in_background(strategy_file, start_date, end_date, initial_cash, task_id)
        )

        return {'message': '回测已开始', 'task_id': task_id}

    except Exception as e:
        logger.error(f'启动回测错误: {e}')
        return {'error': f'启动回测失败: {str(e)}'}


if __name__ == '__main__':
    # 设置日志
    setup_logging()

    print('=' * 60)
    print('QuantBack Socket.IO 回测服务器')
    print('=' * 60)
    print(f'启动时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print('=' * 60)

    # 启动服务器, 同时监听IPv4和IPv6
    uvicorn.run(app, host='', port=8000, log_level='info')
