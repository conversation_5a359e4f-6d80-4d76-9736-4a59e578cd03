<template>
  <div class="card bg-base-100 border">
    <div class="card-body">
      <highcharts ref="chartRef" :options="chartOptions" :constructor-type="'stockChart'" />
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

const props = defineProps({
  backtestStatus: {
    type: Object,
    default: null,
  },
})

// 图表组件引用
const chartRef = ref(null)

// 构建图表系列数据
const buildSeries = () => {
  const portfolioHistory = props.backtestStatus?.results?.portfolio_history
  const series = [
    {
      name: '策略组合',
      data: [],
      color: '#10b981',
      showInNavigator: true,
    },
    {
      name: '基准指数',
      data: [],
      color: '#ef4444',
      showInNavigator: false,
    },
  ]

  // 如果有完整历史数据，填充初始数据
  if (portfolioHistory?.timestamp?.length > 0) {
    const { timestamp, total_value, benchmark_data } = portfolioHistory
    series[0].data = timestamp.map((ts, index) => [ts, total_value[index]])

    if (benchmark_data?.length > 0) {
      series[1].data = timestamp.map((ts, index) => [ts, benchmark_data[index]])
    }
  }

  return series
}

const chartOptions = computed(() => ({
  chart: {
    height: 400,
    backgroundColor: 'transparent',
    style: { fontFamily: 'inherit' },
  },
  title: {
    text: '投资组合收益曲线',
    style: {
      fontSize: '18px',
      fontWeight: 'bold',
    },
  },
  rangeSelector: {
    selected: 4, // 默认选择全部范围
    buttons: [
      {
        type: 'month',
        count: 1,
        text: '1月',
      },
      {
        type: 'month',
        count: 3,
        text: '3月',
      },
      {
        type: 'month',
        count: 6,
        text: '6月',
      },
      {
        type: 'year',
        count: 1,
        text: '1年',
      },
      {
        type: 'all',
        text: '全部',
      },
    ],
  },
  yAxis: {
    labels: {
      format: '{#if (gt value 0)}+{/if}{value}%',
    },
    plotLines: [
      {
        value: 0,
        width: 2,
        color: 'silver',
      },
    ],
    title: {
      text: '累计收益率 (%)',
      style: {
        color: '#666',
      },
    },
  },
  plotOptions: {
    series: {
      compare: 'percent',
      showInNavigator: true,
      lineWidth: 2,
      marker: {
        enabled: false,
        states: {
          hover: {
            enabled: true,
            radius: 5,
          },
        },
      },
    },
  },
  tooltip: {
    pointFormat:
      '<span style="color:{series.color}">' + '{series.name}</span>: <b>{point.y:.2f}%</b><br/>',
    valueDecimals: 2,
    split: true,
    formatter: function () {
      const points = this.points || [this.point]

      let tooltip = `<span style="font-size: 10px">${new Date(this.x).toLocaleDateString()}</span><br/>`

      points.forEach((point) => {
        // 显示相对收益率（由 compare: 'percent' 自动计算）
        tooltip += `<span style="color:${point.series.color}">●</span> <b>${point.series.name}: ${point.y.toFixed(2)}%</b><br/>`

        // 显示原始价值 - 从数据点中获取原始值
        if (point.point && point.point.options && Array.isArray(point.point.options)) {
          const originalValue = point.point.options[1]
          if (point.series.name === '策略组合') {
            tooltip += `<span style="color:#666">组合价值: ¥${originalValue.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span><br/>`
          } else if (point.series.name === '基准指数') {
            tooltip += `<span style="color:#666">指数价格: ${originalValue.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span><br/>`
          }
        }
      })

      return tooltip
    },
  },
  legend: {
    enabled: true,
    align: 'center',
    verticalAlign: 'top',
    layout: 'horizontal',
    itemStyle: {
      fontSize: '12px',
      fontWeight: 'normal',
    },
  },
  navigator: {
    enabled: true,
    height: 40,
  },
  scrollbar: {
    enabled: true,
  },

  credits: {
    enabled: false,
  },
  responsive: {
    rules: [
      {
        condition: {
          maxWidth: 500,
        },
        chartOptions: {
          legend: {
            layout: 'horizontal',
            align: 'center',
            verticalAlign: 'bottom',
          },
          rangeSelector: {
            inputEnabled: false,
          },
        },
      },
    ],
  },
  time: {
    useUTC: false, // 使用本地时区
  },
  series: buildSeries(),
}))

// 实时数据处理
const handleRealtimeResult = (data) => {
  const chart = chartRef.value?.chart
  if (!chart) return

  const { timestamp, total_value, benchmark_value } = data
  const [strategySeries, benchmarkSeries] = chart.series

  strategySeries?.addPoint([timestamp, total_value], false, false)
  if (benchmark_value !== null) {
    benchmarkSeries?.addPoint([timestamp, benchmark_value], false, false)
  }
  chart.redraw()
}

// 监听实时结果
watch(
  () => props.backtestStatus?.realtime_result,
  (data) => {
    if (data) handleRealtimeResult(data)
  },
)

// 监听最终结果，替换所有数据确保正确性
watch(
  () => props.backtestStatus?.results?.portfolio_history,
  (history) => {
    const chart = chartRef.value?.chart
    if (!chart || !history) return

    const { timestamp, total_value, benchmark_data } = history
    const [strategySeries, benchmarkSeries] = chart.series

    strategySeries?.setData(
      timestamp.map((ts, i) => [ts, total_value[i]]),
      false,
    )
    if (benchmark_data?.length > 0) {
      benchmarkSeries?.setData(
        timestamp.map((ts, i) => [ts, benchmark_data[i]]),
        false,
      )
    }
    chart.redraw()
  },
)
</script>
