import gc
import glob
import logging
import os
import re
import time
import zipfile

import pandas as pd
from tqdm import tqdm

from quantback.database.clickhouse_client import get_clickhouse_client
from quantback.utils import convert_stock_code

logger = logging.getLogger(__name__)


def import_1min_data(data_dir, dry_run=False):
    """
    从zip文件中加载1分钟数据到ClickHouse数据库

    Args:
        data_dir (str): 包含zip文件的目录路径
        dry_run (bool): 如果为True，只统计数据条数而不实际插入数据库

    Returns:
    """

    # 统计变量
    total_records = 0
    total_files = 0
    total_zip_files = 0

    try:
        # 只有在非dry_run模式下才连接数据库
        client = None
        if not dry_run:
            client = get_clickhouse_client()

        # 获取所有zip文件
        # 获取目录下所有zip文件
        zip_pattern = os.path.join(data_dir, '*.zip')
        all_zip_files = glob.glob(zip_pattern)

        # 按文件名末尾的数字升序排列
        def extract_number(filename):
            # 提取文件名中的数字，如果没有数字则返回0
            match = re.search(r'(\d+)\.zip$', os.path.basename(filename))
            return int(match.group(1)) if match else 0

        zip_files = sorted(all_zip_files, key=extract_number)

        total_zip_files = len(zip_files)

        if not zip_files:
            logger.warning(f'在目录 {data_dir} 中未找到任何zip文件')
            return
        else:
            logger.info(
                f'找到 {total_zip_files} 个zip文件: {[os.path.basename(f) for f in zip_files]}'
            )

        # 使用tqdm进度条处理zip文件
        for zip_file_path in tqdm(zip_files, desc='处理zip文件'):
            logger.info(f'处理文件: {zip_file_path}')

            try:
                with zipfile.ZipFile(zip_file_path, 'r') as zip_file:
                    # 获取zip文件中的所有文件名
                    file_names = zip_file.namelist()
                    csv_files = [name for name in file_names if name.endswith('.csv')]

                    total_files += len(csv_files)
                    logger.info(
                        f'zip文件 {os.path.basename(zip_file_path)} 包含 {len(csv_files)} 个CSV文件'
                    )

                    # 批量处理CSV文件
                    batch_data = []
                    batch_size = 100  # 每批处理100个文件

                    # 使用tqdm处理CSV文件
                    for i, file_name in enumerate(
                        tqdm(
                            csv_files,
                            desc=f'处理{os.path.basename(zip_file_path)}中的CSV',
                            leave=False,
                        )
                    ):
                        try:
                            # 提取股票代码
                            stock_code = os.path.basename(file_name).replace('.csv', '')
                            stock_code = convert_stock_code(stock_code)

                            # 从zip中读取CSV文件
                            with zip_file.open(file_name) as csv_file_obj:
                                # 读取CSV数据，第一列为索引（时间）
                                df = pd.read_csv(csv_file_obj, index_col=0, parse_dates=True)
                                df.index = pd.to_datetime(df.index).tz_localize('Asia/Shanghai')

                                # 检查必需的列是否存在
                                required_cols = [
                                    'open',
                                    'high',
                                    'low',
                                    'close',
                                    'pre_close',
                                    'volume',
                                    'money',
                                    'high_limit',
                                    'low_limit',
                                ]
                                missing_cols = [
                                    col for col in required_cols if col not in df.columns
                                ]
                                if missing_cols:
                                    logger.error(f'文件 {file_name} 缺少必需列: {missing_cols}')
                                    raise Exception(f'文件 {file_name} 缺少必需列: {missing_cols}')

                                # 准备数据
                                df_processed = pd.DataFrame(
                                    {
                                        'stock_code': stock_code,
                                        'trade_time': df.index,
                                        'open': df['open'],
                                        'high': df['high'],
                                        'low': df['low'],
                                        'close': df['close'],
                                        'pre_close': df['pre_close'],
                                        'volume': df['volume'],
                                        'amount': df['money'],  # money字段映射到amount
                                        'high_limit': df['high_limit'],
                                        'low_limit': df['low_limit'],
                                    }
                                )

                                # 统计记录数
                                total_records += len(df_processed)

                                batch_data.append(df_processed)

                                # 释放内存
                                df = None
                                df_processed = None
                                gc.collect()

                        except Exception as e:
                            logger.error(f'处理文件 {file_name} 时出错: {str(e)}')
                            # 清理已收集的数据
                            batch_data = None
                            gc.collect()
                            # 不关闭连接，复用连接
                            raise Exception(f'处理文件 {file_name} 时出错，终止操作: {str(e)}')

                        # 当达到批量大小或处理完所有文件时，插入数据库
                        if len(batch_data) >= batch_size or i == len(csv_files) - 1:
                            if batch_data:
                                try:
                                    batch_start_time = time.time()

                                    # 过滤掉空的DataFrame
                                    filter_start = time.time()
                                    non_empty_data = [df for df in batch_data if not df.empty]
                                    filter_time = time.time() - filter_start

                                    if non_empty_data:
                                        # 合并所有数据
                                        concat_start = time.time()
                                        combined_df = pd.concat(non_empty_data, ignore_index=True)
                                        concat_time = time.time() - concat_start

                                        # 插入ClickHouse数据库
                                        insert_start = time.time()

                                        # 获取ClickHouse表的字段映射配置
                                        # 内联的字段映射配置，与create_tables_clickhouse.py中的表结构保持一致
                                        clickhouse_columns = [
                                            'stock_code',  # 股票代码
                                            'trade_time',  # 交易时间
                                            'open',  # 开盘价
                                            'high',  # 最高价
                                            'low',  # 最低价
                                            'close',  # 收盘价
                                            'pre_close',  # 前收盘价
                                            'volume',  # 成交量
                                            'amount',  # 成交额（对应CSV中的money字段）
                                            'high_limit',  # 涨停价
                                            'low_limit',  # 跌停价
                                        ]

                                        # 只有在非dry_run模式下才实际插入数据库
                                        if not dry_run:
                                            # 使用insert_df方法，指定column_names确保字段映射正确
                                            # 优势：
                                            # 1. 明确字段映射关系，避免列顺序问题
                                            # 2. 无需转换为列表，节省内存和时间
                                            # 3. 自动处理pandas数据类型映射
                                            # 4. 即使DataFrame列顺序变化也能正确插入
                                            # 5. 集中管理字段配置，便于维护
                                            client.insert_df(
                                                'k_1m',
                                                combined_df[clickhouse_columns],
                                                column_names=clickhouse_columns,
                                            )

                                        insert_time = time.time() - insert_start

                                        batch_total_time = time.time() - batch_start_time
                                        if dry_run:
                                            logger.info(
                                                f'统计 {len(combined_df)} 条记录 - 批次计时: 过滤{filter_time:.3f}s, 合并{concat_time:.3f}s, 总计{batch_total_time:.3f}s'
                                            )
                                        else:
                                            logger.info(
                                                f'成功插入 {len(combined_df)} 条记录 - 批次计时: 过滤{filter_time:.3f}s, 合并{concat_time:.3f}s, 插入{insert_time:.3f}s, 总计{batch_total_time:.3f}s'
                                            )

                                        # 清理内存
                                        combined_df = None
                                    else:
                                        logger.warning('批次中没有有效数据，跳过插入')

                                    cleanup_start = time.time()
                                    batch_data = []
                                    gc.collect()
                                    cleanup_time = time.time() - cleanup_start

                                    if cleanup_time > 0.1:  # 只记录较长的清理时间
                                        logger.info(f'批次gc清理耗时: {cleanup_time:.3f}s')

                                except Exception as e:
                                    logger.error(f'插入数据库时出错: {str(e)}')
                                    batch_data = None
                                    gc.collect()
                                    # 不关闭连接，复用连接
                                    raise Exception(f'插入数据库时出错，终止操作: {str(e)}')

            except Exception as e:
                logger.error(f'处理zip文件 {zip_file_path} 时出错: {str(e)}')
                # 不关闭连接，复用连接
                raise Exception(f'处理zip文件 {zip_file_path} 时出错，终止操作: {str(e)}')

        # 不关闭连接，复用连接

        # 记录统计信息
        if dry_run:
            logger.info(
                f'数据统计完成 - 总zip文件: {total_zip_files}, 总CSV文件: {total_files}, 总记录数: {total_records}'
            )
        else:
            logger.info(
                f'所有1分钟数据加载完成 - 总zip文件: {total_zip_files}, 总CSV文件: {total_files}, 总记录数: {total_records}'
            )

    except Exception as e:
        logger.error(f'加载1分钟数据时出错: {str(e)}')
        raise


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 默认数据目录路径
    default_data_dir = r'D:\stockdata\1min\2020.1.1-2025.6.6etf'
    import_1min_data(default_data_dir, dry_run=False)
